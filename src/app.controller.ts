import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AppService } from './app.service';
@ApiTags('common')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get('/users')
  getHealth(): object {
    return this.appService.getHealth();
  }

  @Get('/health')
  getHealthCheck(): object {
    return this.appService.getHealth();
  }

  @Get('/api/health')
  getApiHealthCheck(): object {
    return this.appService.getHealth();
  }
}
