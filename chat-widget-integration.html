<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Widget Integration Example</title>
    <style>
        /* Main application styles */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* Chat Widget Trigger Button */
        .chat-trigger {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .chat-trigger:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(0,0,0,0.4);
        }

        /* Chat Widget Modal */
        .chat-modal {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 400px;
            height: 600px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            display: none;
            z-index: 1001;
            overflow: hidden;
        }

        .chat-modal.active {
            display: block;
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Chat Widget Header */
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-close {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Chat Widget Content */
        .chat-content {
            height: calc(100% - 60px);
        }

        .chat-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .chat-modal {
                bottom: 0;
                right: 0;
                left: 0;
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }
            
            .chat-trigger {
                bottom: 15px;
                right: 15px;
            }
        }

        /* Notification Badge */
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Your main application content -->
    <div class="main-content">
        <h1>Your Main Application</h1>
        <p>This is your existing application content. The chat widget is available in the bottom-right corner.</p>
        
        <div style="margin: 20px 0;">
            <button onclick="openChat()" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">
                Open Chat Widget
            </button>
        </div>
        
        <p>You can integrate this chat widget into any page of your application. It provides a non-intrusive way for users to access the AI chat functionality.</p>
    </div>

    <!-- Chat Widget Trigger Button -->
    <button class="chat-trigger" onclick="toggleChat()" id="chatTrigger">
        💬
        <span class="notification-badge" id="notificationBadge" style="display: none;">1</span>
    </button>

    <!-- Chat Widget Modal -->
    <div class="chat-modal" id="chatModal">
        <div class="chat-header">
            <h3 style="margin: 0; font-size: 16px;">AI Assistant</h3>
            <button class="chat-close" onclick="closeChat()">×</button>
        </div>
        <div class="chat-content">
            <iframe 
                class="chat-iframe" 
                id="chatIframe"
                src=""
                title="AI Chat Assistant">
            </iframe>
        </div>
    </div>

    <script>
        let chatOpen = false;
        const chatModal = document.getElementById('chatModal');
        const chatIframe = document.getElementById('chatIframe');
        const notificationBadge = document.getElementById('notificationBadge');

        // Configuration
        const CHAT_URL = 'http://localhost:3001/streaming-ui.html'; // Update this to your actual URL
        
        function toggleChat() {
            if (chatOpen) {
                closeChat();
            } else {
                openChat();
            }
        }

        function openChat() {
            // Load iframe content only when opened (performance optimization)
            if (!chatIframe.src) {
                chatIframe.src = CHAT_URL;
            }
            
            chatModal.classList.add('active');
            chatOpen = true;
            hideNotification();
        }

        function closeChat() {
            chatModal.classList.remove('active');
            chatOpen = false;
        }

        function showNotification() {
            notificationBadge.style.display = 'flex';
        }

        function hideNotification() {
            notificationBadge.style.display = 'none';
        }

        // Listen for messages from the iframe (optional)
        window.addEventListener('message', function(event) {
            // Handle messages from the chat iframe
            if (event.origin !== 'http://localhost:3001') return; // Security check
            
            if (event.data.type === 'newMessage') {
                if (!chatOpen) {
                    showNotification();
                }
            }
        });

        // Close chat when clicking outside (optional)
        document.addEventListener('click', function(event) {
            if (chatOpen && !chatModal.contains(event.target) && !event.target.closest('.chat-trigger')) {
                closeChat();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && chatOpen) {
                closeChat();
            }
        });
    </script>
</body>
</html>
